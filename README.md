# FastRTC Gemini Voice Agent with Local Whisper

This project demonstrates voice interactions with AI assistants using FastRTC, Google Gemini for chat, and local Whisper for speech-to-text.

## Features

- **Speech Recognition**: Local Whisper model for accurate transcription
- **Chat Model**: Google Gemini 1.5 Pro for intelligent responses
- **Text-to-Speech**: Local TTS using pyttsx3 (system TTS engines)
- **Math Agent**: Specialized agent for mathematical calculations
- **Real-time Voice**: FastRTC for low-latency voice interactions

## Setup

1. Set up Python environment and install dependencies:
   ```bash
   uv venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   uv sync
   ```

2. Set up environment variables:
   - Create a `.env` file in the project root
   - Add your Google API key: `GOOGLE_API_KEY=your_api_key_here`
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

3. Install system dependencies (if needed):
   - **Windows**: TTS should work out of the box with SAPI
   - **Linux**: Install espeak: `sudo apt-get install espeak espeak-data libespeak-dev`
   - **macOS**: TTS should work with built-in system voices

## Running the Application

Navigate to the src directory:
```bash
cd src
```

Run with web UI (default):
```bash
python fastrtc_groq_voice_stream.py
```

Run with phone interface (get a temporary phone number):
```bash
python fastrtc_groq_voice_stream.py --phone
```

## Configuration

### Whisper Model Size
You can configure the Whisper model size by modifying the `get_whisper_processor()` call in the code:
- `tiny`: Fastest, least accurate (~39 MB)
- `base`: Good balance of speed and accuracy (~74 MB) - **Default**
- `small`: Better accuracy (~244 MB)
- `medium`: Even better accuracy (~769 MB)
- `large`: Best accuracy (~1550 MB)

### TTS Configuration
The system uses pyttsx3 for local text-to-speech. You can modify voice properties in `process_local_whisper.py`:
- Speech rate (words per minute)
- Voice selection
- Volume level

## Usage Examples

### Math Agent Commands

- "What is 5 plus 7?"
- "Can you multiply 12 and 4?"
- "Calculate the sum of 123 and 456"

### General Conversation

The agent can handle general conversation topics while maintaining access to math tools when needed.

## Troubleshooting

### Common Issues

1. **Whisper model download**: First run will download the selected Whisper model
2. **TTS not working**: Ensure system TTS is properly configured
3. **CUDA support**: Whisper will automatically use GPU if available
4. **API key issues**: Verify your Google API key is correctly set in the environment

### Performance Tips

- Use smaller Whisper models for faster transcription
- Ensure good microphone quality for better recognition
- Consider using GPU acceleration for larger Whisper models
