[project]
name = "fastrtc-openai-voice-agent"
version = "0.1.0"
description = "A simple voice agent using FastRTC, OpenAI, and speech recognition"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastrtc[vad]>=0.0.19",
    "openai>=1.0.0",
    "langchain-openai>=0.1.0",
    "SpeechRecognition>=3.10.0",
    "numpy>=2.1.3",
    "langgraph>=0.1.18",
    "langchain-core>=0.1.29",
    "loguru>=0.7.3",
    "pydub>=0.25.1",
    "pyttsx3>=2.90",
    "python-dotenv>=1.0.0",
]
