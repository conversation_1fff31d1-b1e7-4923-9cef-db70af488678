[project]
name = "fastrtc-gemini-whisper"
version = "0.1.0"
description = "A simple voice agent using FastRTC, Gemini, and local Whisper"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastrtc[vad]>=0.0.19",
    "google-generativeai>=0.8.0",
    "langchain-google-genai>=2.0.0",
    "openai-whisper>=20231117",
    "numpy>=2.1.3",
    "langgraph>=0.1.18",
    "langchain-core>=0.1.29",
    "loguru>=0.7.3",
    "pydub>=0.25.1",
    "torch>=2.0.0",
    "torchaudio>=2.0.0",
    "pyttsx3>=2.90",
]
