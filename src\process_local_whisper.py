import os
import tempfile
import wave
from typing import Any, Generator, <PERSON><PERSON>

import numpy as np
from pydub import AudioSegment
import io

# Try to import whisper, fall back to speech_recognition if not available
try:
    import whisper
    import torch
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    print("Whisper not available, falling back to speech_recognition")

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    print("speech_recognition not available")


class LocalWhisperProcessor:
    """Local speech processor for transcription operations."""

    def __init__(self, model_size: str = "base"):
        """
        Initialize the speech processor.

        Args:
            model_size: Whisper model size ("tiny", "base", "small", "medium", "large") - only used if Whisper is available
        """
        self.use_whisper = WHISPER_AVAILABLE
        self.use_speech_recognition = SPEECH_RECOGNITION_AVAILABLE

        if self.use_whisper:
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"Loading Whisper model '{model_size}' on {self.device}...")
            self.model = whisper.load_model(model_size, device=self.device)
            print("Whisper model loaded successfully!")
        elif self.use_speech_recognition:
            print("Using speech_recognition for transcription...")
            self.recognizer = sr.Recognizer()
        else:
            print("Warning: No speech recognition available. Transcription will return placeholder text.")
    
    def transcribe_audio(self, audio_tuple: tuple[int, np.ndarray]) -> str:
        """
        Transcribe audio using available speech recognition method.

        Args:
            audio_tuple: Tuple containing (sample_rate, audio_data)

        Returns:
            Transcribed text
        """
        sample_rate, audio_data = audio_tuple

        # Create temporary file for processing
        temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
        temp_file_path = temp_file.name
        temp_file.close()

        try:
            # Convert numpy array to audio file
            # Ensure audio_data is in the right format
            if audio_data.ndim > 1:
                audio_data = audio_data.flatten()

            # Normalize audio data to int16 range
            if audio_data.dtype != np.int16:
                audio_data = (audio_data * 32767).astype(np.int16)

            # Write to WAV file
            with wave.open(temp_file_path, "wb") as wf:
                wf.setnchannels(1)  # Mono
                wf.setsampwidth(2)  # 16-bit
                wf.setframerate(sample_rate)
                wf.writeframes(audio_data.tobytes())

            # Try different transcription methods
            if self.use_whisper:
                # Use Whisper
                result = self.model.transcribe(temp_file_path)
                return result["text"].strip()
            elif self.use_speech_recognition:
                # Use speech_recognition with Google's free service
                with sr.AudioFile(temp_file_path) as source:
                    audio = self.recognizer.record(source)
                try:
                    text = self.recognizer.recognize_google(audio)
                    return text.strip()
                except sr.UnknownValueError:
                    return "Could not understand audio"
                except sr.RequestError as e:
                    return f"Error with speech recognition service: {e}"
            else:
                # Fallback - return placeholder
                return "Speech recognition not available - please install openai-whisper or SpeechRecognition"

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)


def process_local_tts(
    text: str,
) -> Generator[Tuple[int, np.ndarray], None, None]:
    """
    Process text-to-speech using pyttsx3 (local TTS engine).

    This implementation uses pyttsx3 which provides cross-platform TTS capabilities
    using the system's built-in TTS engines (SAPI on Windows, espeak on Linux, etc.)

    Args:
        text: Text to convert to speech

    Yields:
        A tuple of (sample_rate, audio_array) for audio playback
    """
    try:
        import pyttsx3

        # Initialize TTS engine
        engine = pyttsx3.init()

        # Configure voice properties
        voices = engine.getProperty('voices')
        if voices:
            # Use the first available voice
            engine.setProperty('voice', voices[0].id)

        # Set speech rate (words per minute)
        engine.setProperty('rate', 180)

        # Set volume (0.0 to 1.0)
        engine.setProperty('volume', 0.8)

        # Create temporary file for audio output
        temp_file = tempfile.NamedTemporaryFile(suffix=".wav", delete=False)
        temp_file_path = temp_file.name
        temp_file.close()

        try:
            # Save speech to file
            engine.save_to_file(text, temp_file_path)
            engine.runAndWait()

            # Read the generated audio file
            with wave.open(temp_file_path, "rb") as wf:
                sample_rate = wf.getframerate()
                n_frames = wf.getnframes()
                audio_data = wf.readframes(n_frames)

            # Convert to numpy array
            audio_array = np.frombuffer(audio_data, dtype=np.int16).reshape(1, -1)
            yield (sample_rate, audio_array)

        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    except ImportError:
        print("Warning: pyttsx3 not available, falling back to placeholder TTS")
        # Fallback to simple beep sound
        sample_rate = 22050
        duration = len(text) * 0.1  # Rough estimate based on text length
        duration = max(1.0, min(duration, 10.0))  # Clamp between 1-10 seconds

        # Generate a simple tone as placeholder
        t = np.linspace(0, duration, int(sample_rate * duration))
        frequency = 440  # A4 note
        audio_data = (np.sin(2 * np.pi * frequency * t) * 0.1 * 32767).astype(np.int16)

        # Reshape to match expected format
        audio_array = audio_data.reshape(1, -1)

        yield (sample_rate, audio_array)

    except Exception as e:
        print(f"Error in TTS processing: {e}")
        # Fallback to silence
        sample_rate = 22050
        duration = 1.0
        audio_data = np.zeros(int(sample_rate * duration), dtype=np.int16)
        audio_array = audio_data.reshape(1, -1)
        yield (sample_rate, audio_array)


# Global instance
whisper_processor = None

def get_whisper_processor(model_size: str = None) -> LocalWhisperProcessor:
    """Get or create a global Whisper processor instance."""
    global whisper_processor
    if whisper_processor is None:
        # Use environment variable or default to 'base'
        if model_size is None:
            model_size = os.getenv("WHISPER_MODEL_SIZE", "base")
        whisper_processor = LocalWhisperProcessor(model_size)
    return whisper_processor
